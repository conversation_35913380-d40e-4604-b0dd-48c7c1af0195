CREATE OR REPLACE FUNCTION public.tms_create_booking_across_slots(ins_id_ bigint, form_data_ json)
 RETURNS json
 LANGUAGE plpgsql
AS $function$
DECLARE
    -- Basic status/response variables
    status boolean := false;
    message text := 'Internal_error';
    resp_data json;

    -- Variables from form_data
    org_id_ integer;
    usr_id_ uuid;
    ip_address_ text;
    user_agent_ text;
    capacity_id_ bigint;
    srvc_type_id_ int;
    booking_details_ json;
    resource_id_ text;

    -- Booking requirement variables
    required_man_minutes_ integer;
    booking_qty_ integer;
    existing_booking_ids_ int[];
    existing_booking_id_ int;
    booking_ids int[] := '{}';

    -- Slot variables
    first_slot_capacity_id bigint;
    first_slot_day date;
    first_slot_start_time time;
    current_capacity_id bigint;
    remaining_capacity integer;
    total_available_capacity integer := 0;

    -- Loop record
    slot_record RECORD;

    -- JSON for service request update
    user_context_json json;
    booking_update_json json;

    -- Slot list
    adjacent_slots_ids bigint[] := '{}';
   
    -- Booking status variables for timeline
    is_booked boolean := false;
    booking_message text;

begin
	raise notice 'form_data_ %',form_data_;
    -- Extract data from form_data
    org_id_ := (form_data_->>'org_id')::integer;
    usr_id_ := (form_data_->>'usr_id')::uuid;
    ip_address_ := form_data_->>'ip_address';
    user_agent_ := form_data_->>'user_agent';
    srvc_type_id_ := form_data_->>'srvc_type_id';
    booking_details_ := form_data_->>'booking_data';
    capacity_id_ := (form_data_->'booking_data'->>'capacity_id')::bigint;

    -- Check if existing booking exists
    SELECT array_agg(db_id)
    INTO existing_booking_ids_
    FROM cl_tx_bookings
    WHERE order_id = ins_id_ AND (is_cancelled IS NULL OR is_cancelled = false);

    -- Step 1: Check available capacity before cancelling
    SELECT resource_id
    INTO resource_id_
    FROM cl_tx_capacity
    WHERE db_id = capacity_id_;

    IF resource_id_ IS NULL THEN
        RETURN json_build_object('status', false, 'message', 'Resource not found');
    END IF;

    -- Get total required man-minutes for the new booking
    required_man_minutes_ := COALESCE((form_data_->>'required_man_minutes_')::integer, 0);

    -- Find the first available slot
    SELECT db_id, usr_tmzone_day, start_time
    INTO first_slot_capacity_id, first_slot_day, first_slot_start_time
    FROM cl_tx_capacity
    WHERE resource_id = resource_id_
      AND available_capacity > 0
      AND usr_tmzone_day >= '2025-08-13'
      AND utilization < 1
    ORDER BY usr_tmzone_day ASC, start_time ASC
    LIMIT 1;

    -- If no slot is available, fail immediately
    IF first_slot_capacity_id IS NULL THEN
        RETURN json_build_object('status', false, 'message', 'Booking failed - No available slots found');
    END IF;
   
     -- Add first slot
    adjacent_slots_ids := array_append(adjacent_slots_ids, first_slot_capacity_id);

    -- Add next chronological slot (dynamic duration handling)
    SELECT db_id
    INTO current_capacity_id
    FROM cl_tx_capacity
    WHERE resource_id = resource_id_
      AND (
            usr_tmzone_day > first_slot_day
         OR (usr_tmzone_day = first_slot_day AND start_time ::time > first_slot_start_time ::time)
      )
      AND available_capacity > 0
      AND utilization < 1
      AND booked_cap_in_minutes < total_cap_in_minutes
    ORDER BY usr_tmzone_day ASC, start_time ASC
    LIMIT 1;

    IF current_capacity_id IS NOT NULL THEN
        adjacent_slots_ids := array_append(adjacent_slots_ids, current_capacity_id);
    END IF;
   
   raise notice 'adjacent_slots_ids %', adjacent_slots_ids;

    -- Check if the available capacity is sufficient before proceeding
    SELECT COALESCE(SUM(total_cap_in_minutes - booked_cap_in_minutes), 0)
    INTO total_available_capacity
    FROM cl_tx_capacity
    WHERE db_id = ANY(adjacent_slots_ids)
      AND available_capacity > 0
      AND utilization < 1
      AND booked_cap_in_minutes < total_cap_in_minutes;

    -- If required man-minutes exceed available capacity, return failure
    IF total_available_capacity < required_man_minutes_ THEN
        -- Rollback changes: Do not proceed with booking, return a failure message
        is_booked := false;
        booking_message := 'Booking failed - Insufficient capacity in selected slots. Required: ' || required_man_minutes_ || ' minutes, Available: ' || total_available_capacity || ' minutes
        ';
       RETURN json_build_object(
        'status', false,
        'message', 'Booking failed - Insufficient capacity in selected slots. Required: ' || required_man_minutes_ || ' minutes, Available: ' || total_available_capacity || ' minutes'
    );
    END IF;
   
   raise notice 'existing_booking_ids_ %',required_man_minutes_;
    raise notice 'existing_booking_ids_ %',total_available_capacity;

    -- Step 2: Release capacity from cancelled bookings
    IF existing_booking_ids_ IS NOT NULL AND array_length(existing_booking_ids_, 1) > 0 THEN
        -- Mark existing bookings as cancelled
        UPDATE cl_tx_bookings
        SET is_cancelled = true,
            u_meta = row(ip_address_, user_agent_, now() at time zone 'utc')
        WHERE db_id = ANY(existing_booking_ids_);

        -- Release capacity from cancelled bookings
        UPDATE cl_tx_capacity
        SET booked_cap_in_minutes = booked_cap_in_minutes - b.booked_qty,
            u_meta = row(ip_address_, user_agent_, now() at time zone 'utc')
        FROM cl_tx_bookings b
        WHERE cl_tx_capacity.db_id = b.capacity_id
          AND b.db_id = ANY(existing_booking_ids_);
    END IF;

    -- Step 3: Allocate new booking slots
    FOR slot_record IN
        SELECT db_id, total_cap_in_minutes, booked_cap_in_minutes, start_time
        FROM cl_tx_capacity
        WHERE db_id = ANY(adjacent_slots_ids)
          AND available_capacity > 0
          AND utilization < 1
          AND booked_cap_in_minutes < total_cap_in_minutes
        ORDER BY usr_tmzone_day ASC, start_time ASC
    LOOP
        EXIT WHEN required_man_minutes_ <= 0;

        remaining_capacity := slot_record.total_cap_in_minutes - slot_record.booked_cap_in_minutes;
        booking_qty_ := LEAST(required_man_minutes_, remaining_capacity);
        raise notice 'remaining_capacity %',remaining_capacity;
        INSERT INTO cl_tx_bookings (
            capacity_id, order_id, order_label, booked_qty, is_cancelled, c_meta, u_meta
        ) VALUES (
            slot_record.db_id, ins_id_, 'Service Request', booking_qty_, false,
            row(ip_address_, user_agent_, now() at time zone 'utc'),
            row(ip_address_, user_agent_, now() at time zone 'utc')
        ) RETURNING db_id INTO existing_booking_id_;

        booking_ids := array_append(booking_ids, existing_booking_id_::int);
        required_man_minutes_ := required_man_minutes_ - booking_qty_;

        UPDATE cl_tx_capacity
        SET booked_cap_in_minutes = booked_cap_in_minutes + booking_qty_
        WHERE db_id = slot_record.db_id;
    END LOOP;

    -- Step 4: Check if the booking was successful
    IF required_man_minutes_ <= 0 THEN
        status := true;
        message := 'Booking completed in first slot and its immediate adjacent slots';
        is_booked := true;
        booking_message := 'Booking successful - Completed across first slot and adjacent slots';
    ELSE
        status := false;
        message := 'Booking failed - Could not complete booking in first slot and its immediate adjacent slots';
        is_booked := false;
        booking_message := 'Booking failed - Could not complete booking in first slot and adjacent slots';
    END IF;

    -- Update service request if success
    IF status THEN
        resp_data := json_build_object(
            'booking_id', existing_booking_id_,
            'capacity_id', first_slot_capacity_id,
            'booking_details', booking_details_
        );

        user_context_json := json_build_object(
            'org_id', org_id_,
            'usr_id', usr_id_,
            'ip_address', ip_address_,
            'user_agent', user_agent_,
            'srvc_type_id', srvc_type_id_
        );

        booking_update_json := jsonb_set(user_context_json::jsonb, '{booking_id}', to_jsonb(booking_ids), true);
        booking_update_json := jsonb_set(booking_update_json::jsonb, '{capacity_id}', to_jsonb(first_slot_capacity_id), true);
        booking_update_json := jsonb_set(booking_update_json::jsonb, '{booking_data}', to_jsonb(resp_data->'booking_details'), true);
	    booking_update_json := jsonb_set(booking_update_json::jsonb, '{is_booked}', to_jsonb(is_booked), true);
		booking_update_json := jsonb_set(booking_update_json::jsonb, '{booking_message}', to_jsonb(booking_message), true);

        PERFORM tms_create_service_request(booking_update_json, ins_id_::integer);

        resp_data := json_build_object('status', status, 'message', message, 'booking_ids', booking_ids);
    ELSE
        -- Return failure response if booking is not successful
        user_context_json := json_build_object(
            'org_id', org_id_,
            'usr_id', usr_id_,
            'ip_address', ip_address_,
            'user_agent', user_agent_,
            'srvc_type_id', srvc_type_id_
        );

        booking_update_json := jsonb_set(user_context_json::jsonb, '{is_booked}', to_jsonb(is_booked), true);
        booking_update_json := jsonb_set(booking_update_json::jsonb, '{booking_message}', to_jsonb(booking_message), true);

        -- Call tms_create_service_request to update with booking failure status
        PERFORM tms_create_service_request(booking_update_json, ins_id_::integer);

        resp_data := json_build_object('status', status, 'message', message);
    END IF;

    RETURN resp_data;
END;
$function$
;

CREATE OR REPLACE FUNCTION public.tms_create_booking_across_slots(ins_id_ bigint, form_data_ json)
 RETURNS json
 LANGUAGE plpgsql
AS $function$
DECLARE
    -- Existing declarations
    status boolean := false;
    message text := 'Internal_error';
    affected_rows integer;
    resp_data json;

    -- Variables from form_data
    org_id_ integer;
    usr_id_ uuid;
    ip_address_ text;
    user_agent_ text;
    capacity_id_ bigint;
    srvc_type_id_ int;
    booking_details_ json;
    resource_id_ text;

    -- Capacity variables
    available_capacity_ integer;
    total_cap_in_minutes_ integer;
    booked_cap_in_minutes_ integer;
    remaining_capacity_ integer;

    -- Booking table variables
    existing_booking_id_ int;
    booking_qty_ integer;

    -- Timeline and update variables
    user_context_json json;
    booking_update_json json;

    -- Loop variables
    required_man_minutes_ integer;
    slot_duration_ integer := 60; -- Default slot duration
    current_capacity_id bigint; -- Track capacity ID for the current slot
    booking_ids int[] := '{}';  -- Array to store booking IDs
    remaining_capacity integer;

    -- Variables for first slot and adjacent slots logic
    first_slot_capacity_id bigint;
    first_slot_day date;
    first_slot_start_time time;
    slot_record RECORD;
    total_available_capacity integer := 0;
    adjacent_slots_ids bigint[] := '{}'; -- Array to store first slot and its adjacent slots

BEGIN
    -- Extract data from form_data
    org_id_ := (form_data_->>'org_id')::integer;
    usr_id_ := (form_data_->>'usr_id')::uuid;
    ip_address_ := form_data_->>'ip_address';
    user_agent_ := form_data_->>'user_agent';
    srvc_type_id_ := form_data_->>'srvc_type_id';
    booking_details_ := form_data_->>'booking_data';
    capacity_id_ := (form_data_->'booking_data'->>'capacity_id')::bigint;

    -- Get the resource_id associated with the capacity_id
    SELECT resource_id
    INTO resource_id_
    FROM cl_tx_capacity
    WHERE db_id = capacity_id_;

    IF resource_id_ IS NULL THEN
        RETURN json_build_object('status', false, 'message', 'Resource not found');
    END IF;

    -- Calculate the total man-minutes required (passed from form_data)
    required_man_minutes_ := COALESCE((form_data_->>'required_man_minutes_')::integer, 0);

    -- First, get the first available slot
    SELECT db_id, usr_tmzone_day, usr_tmzone_start_time
    INTO first_slot_capacity_id, first_slot_day, first_slot_start_time
    FROM cl_tx_capacity
    WHERE resource_id = resource_id_
      AND available_capacity > 0
      AND usr_tmzone_day >= CURRENT_DATE
      AND utilization < 1
    ORDER BY usr_tmzone_day ASC, usr_tmzone_start_time ASC
    LIMIT 1;

    -- If no first slot found, reject booking
    IF first_slot_capacity_id IS NULL THEN
        status := false;
        message := 'No available slots found for booking';
        RETURN json_build_object('status', status, 'message', message);
    END IF;

    -- Build array of first slot and its immediate adjacent slots only
    -- Add the first slot
    adjacent_slots_ids := array_append(adjacent_slots_ids, first_slot_capacity_id);

    -- Add previous adjacent slot (if exists and available)
    SELECT db_id INTO current_capacity_id
    FROM cl_tx_capacity
    WHERE resource_id = resource_id_
      AND usr_tmzone_day = first_slot_day
      AND usr_tmzone_start_time = first_slot_start_time - INTERVAL '1 hour'
      AND available_capacity > 0
      AND utilization < 1
      AND booked_cap_in_minutes < total_cap_in_minutes;

    IF current_capacity_id IS NOT NULL THEN
        adjacent_slots_ids := array_append(adjacent_slots_ids, current_capacity_id);
    END IF;

    -- Add next adjacent slot (if exists and available)
    SELECT db_id INTO current_capacity_id
    FROM cl_tx_capacity
    WHERE resource_id = resource_id_
      AND usr_tmzone_day = first_slot_day
      AND usr_tmzone_start_time = first_slot_start_time + INTERVAL '1 hour'
      AND available_capacity > 0
      AND utilization < 1
      AND booked_cap_in_minutes < total_cap_in_minutes;

    IF current_capacity_id IS NOT NULL THEN
        adjacent_slots_ids := array_append(adjacent_slots_ids, current_capacity_id);
    END IF;

    -- Calculate total available capacity in first slot and its immediate adjacent slots only
    SELECT COALESCE(SUM(total_cap_in_minutes - booked_cap_in_minutes), 0)
    INTO total_available_capacity
    FROM cl_tx_capacity
    WHERE db_id = ANY(adjacent_slots_ids)
      AND available_capacity > 0
      AND utilization < 1
      AND booked_cap_in_minutes < total_cap_in_minutes;

    -- Check if total available capacity can cover required man-minutes
    IF total_available_capacity < required_man_minutes_ THEN
        status := false;
        message := 'Insufficient capacity in first slot and its immediate adjacent slots. Required: ' || required_man_minutes_ || ', Available: ' || total_available_capacity;
        RETURN json_build_object('status', status, 'message', message);
    END IF;

    -- If capacity is sufficient, proceed with booking
    -- Book slots starting from first slot and its immediate adjacent slots only
    FOR slot_record IN
        SELECT db_id, available_capacity, total_cap_in_minutes, booked_cap_in_minutes, usr_tmzone_start_time
        FROM cl_tx_capacity
        WHERE db_id = ANY(adjacent_slots_ids)
          AND available_capacity > 0
          AND utilization < 1
          AND booked_cap_in_minutes < total_cap_in_minutes
        ORDER BY usr_tmzone_start_time ASC
    LOOP
        -- Exit if all required man-minutes are booked
        EXIT WHEN required_man_minutes_ <= 0;

        remaining_capacity := slot_record.total_cap_in_minutes - slot_record.booked_cap_in_minutes;

        -- Calculate how many man-minutes to book in this slot
        booking_qty_ := LEAST(required_man_minutes_, remaining_capacity);

        -- Update booking table with the current slot's booking
        INSERT INTO cl_tx_bookings (
            capacity_id, order_id, order_label, booked_qty, c_meta, u_meta
        ) VALUES (
            slot_record.db_id, ins_id_, 'Service Request', booking_qty_,
            row(ip_address_, user_agent_, now() at time zone 'utc'),
            row(ip_address_, user_agent_, now() at time zone 'utc')
        ) RETURNING db_id INTO existing_booking_id_;

        -- Collect the booking ID
        booking_ids := array_append(booking_ids, existing_booking_id_::int);

        -- Reduce the remaining man-minutes to be booked
        required_man_minutes_ := required_man_minutes_ - booking_qty_;

        -- Update capacity table with the new booked amount for the current slot
        UPDATE cl_tx_capacity
        SET booked_cap_in_minutes = booked_cap_in_minutes + booking_qty_
        WHERE db_id = slot_record.db_id;

    END LOOP;

    -- Check if booking was successful
    IF required_man_minutes_ <= 0 THEN
        status := true;
        message := 'Booking completed in first slot and its immediate adjacent slots';
    ELSE
        status := false;
        message := 'Booking failed - could not complete booking in first slot and its immediate adjacent slots';
    END IF;

    -- After booking, call tms_create_service_request to update with booking IDs
    IF status is true then
        resp_data := json_build_object(
                'booking_id', existing_booking_id_,
                'capacity_id', first_slot_capacity_id, -- Use first slot capacity ID
                'booking_details', booking_details_
            );

            -- Get user context for updating service request
            user_context_json := json_build_object(
                'org_id', org_id_,
                'usr_id', usr_id_,
                'ip_address', ip_address_,
                'user_agent', user_agent_,
                'srvc_type_id',srvc_type_id_
            );

           -- Convert user_context_json to jsonb before using jsonb_set
			booking_update_json := jsonb_set(user_context_json::jsonb, '{booking_id}', to_jsonb(booking_ids), true);
			booking_update_json := jsonb_set(booking_update_json ::jsonb, '{capacity_id}', to_jsonb(first_slot_capacity_id), true);
			booking_update_json := jsonb_set(booking_update_json::jsonb, '{booking_data}', to_jsonb(resp_data->'booking_details'), true);
            -- Call tms_create_service_request again to update booking_ids, booking_details, capacity_id columns
		    raise notice 'booking_update_json %',booking_update_json;

        -- Call tms_create_service_request to update the service request with the booking IDs
        PERFORM tms_create_service_request(booking_update_json, ins_id_::integer);

        -- Return the final response
        resp_data := json_build_object('status', status, 'message', message, 'booking_ids', booking_ids);
    ELSE
        -- Return failure response if booking is not successful
        resp_data := json_build_object('status', status, 'message', message);
    END IF;

    -- Return the response
    RETURN resp_data;
END;
$function$
;

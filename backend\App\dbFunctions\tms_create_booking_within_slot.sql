CREATE OR REPLACE FUNCTION public.tms_create_booking_within_slot(ins_id_ bigint, form_data_ json)
RETURNS json
LANGUAGE plpgsql
AS $function$
DECLARE
    status boolean := false;
    message text := 'Internal_error';
    affected_rows integer;
    resp_data json;

    org_id_ integer;
    usr_id_ uuid;
    ip_address_ text;
    user_agent_ text;
    capacity_id_ bigint;
    srvc_type_id_ int;
    booking_details_ json;

    manpower_ integer;
    job_duration_ integer;
    hub_travel_time_ integer := 20;
    total_duration_ integer;
    required_man_minutes_ integer;

    available_capacity_ integer;
    total_cap_in_minutes_ integer;
    booked_cap_in_minutes_ integer;
    remaining_capacity_ integer;

    existing_booking_id_ int;
    existing_booking_qty_ integer;
    existing_capacity_id_ bigint;
    booking_qty_ integer;
    booking_ids int[] := '{}';

    user_context_json json;
    booking_update_json json;

    -- Booking status variables for timeline
    is_booked boolean := false;
    booking_message text;
BEGIN
    -- Extract data
    org_id_ := (form_data_->>'org_id')::integer;
    usr_id_ := (form_data_->>'usr_id')::uuid;
    ip_address_ := form_data_->>'ip_address';
    user_agent_ := form_data_->>'user_agent';
    srvc_type_id_ := form_data_->>'srvc_type_id';
    booking_details_ := form_data_->>'booking_data';
    capacity_id_ := (form_data_->'booking_data'->>'capacity_id')::bigint;

    IF ins_id_ IS NULL OR capacity_id_ IS NULL THEN
        is_booked := false;
        booking_message := 'Booking failed - ins_id and capacity_id are required';
        RETURN json_build_object('status', false, 'message', 'ins_id and capacity_id are required');
    END IF;

    -- Booking parameters
    manpower_ := COALESCE((form_data_->>'6b858839-f154-4573-9e9b-4b01ebeb44a7')::integer, 1);
    job_duration_ := COALESCE((form_data_->>'3151dffe-45c3-4c27-9008-0a01c0205374')::integer, 60);

    total_duration_ := job_duration_ + hub_travel_time_;
    required_man_minutes_ := total_duration_ * manpower_;

    -- Get available capacity
    SELECT available_capacity, total_cap_in_minutes, booked_cap_in_minutes
    INTO available_capacity_, total_cap_in_minutes_, booked_cap_in_minutes_
    FROM cl_tx_capacity
    WHERE db_id = capacity_id_
      AND utilization < 1
      AND usr_tmzone_day >= CURRENT_DATE;

    IF available_capacity_ IS NULL THEN
        is_booked := false;
        booking_message := 'Booking failed - Capacity not found';
        RETURN json_build_object('status', false, 'message', 'Capacity not found');
    END IF;

    remaining_capacity_ := total_cap_in_minutes_ - booked_cap_in_minutes_;

    -- Check if booking already exists
    SELECT db_id, booked_qty, capacity_id
    INTO existing_booking_id_, existing_booking_qty_, existing_capacity_id_
    FROM cl_tx_bookings
    WHERE order_id = ins_id_
    LIMIT 1;

    booking_qty_ := required_man_minutes_;

    IF existing_booking_id_ IS NOT NULL THEN
        -- Case: Updating existing booking
        IF existing_capacity_id_ = capacity_id_ THEN
            -- Same capacity_id → just update booked_qty
            UPDATE cl_tx_bookings
               SET booked_qty = booking_qty_,
                   u_meta = row(ip_address_, user_agent_, now() at time zone 'utc')
             WHERE db_id = existing_booking_id_;
            GET DIAGNOSTICS affected_rows = ROW_COUNT;
            IF affected_rows = 1 THEN
                message := 'Booking updated successfully (same capacity)';
                is_booked := true;
                booking_message := 'Booking updated successfully within same slot';
            ELSE
                is_booked := false;
                booking_message := 'Booking failed - Failed to update booking';
                RETURN json_build_object('status', false, 'message', 'Failed to update booking');
            END IF;
        ELSE
            -- Capacity changed → release old capacity and update booking
            UPDATE cl_tx_capacity
               SET booked_cap_in_minutes = booked_cap_in_minutes - existing_booking_qty_,
                   u_meta = row(ip_address_, user_agent_, now() at time zone 'utc')
             WHERE db_id = existing_capacity_id_;

            UPDATE cl_tx_bookings
               SET capacity_id = capacity_id_,
                   booked_qty = booking_qty_,
                   u_meta = row(ip_address_, user_agent_, now() at time zone 'utc')
             WHERE db_id = existing_booking_id_;

            UPDATE cl_tx_capacity
               SET booked_cap_in_minutes = booked_cap_in_minutes + booking_qty_,
                   u_meta = row(ip_address_, user_agent_, now() at time zone 'utc')
             WHERE db_id = capacity_id_;

            message := 'Booking updated successfully (capacity changed)';
            is_booked := true;
            booking_message := 'Booking updated successfully with capacity change';
        END IF;

        booking_ids := array_append(booking_ids, existing_booking_id_::int);
    ELSE
        -- New booking
        INSERT INTO cl_tx_bookings (
            capacity_id, order_id, order_label, booked_qty, c_meta, u_meta
        ) VALUES (
            capacity_id_, ins_id_, 'Service Request', booking_qty_,
            row(ip_address_, user_agent_, now() at time zone 'utc'),
            row(ip_address_, user_agent_, now() at time zone 'utc')
        )
        RETURNING db_id INTO existing_booking_id_;

        booking_ids := array_append(booking_ids, existing_booking_id_::int);

        UPDATE cl_tx_capacity
           SET booked_cap_in_minutes = booked_cap_in_minutes + booking_qty_,
               u_meta = row(ip_address_, user_agent_, now() at time zone 'utc')
         WHERE db_id = capacity_id_;

        message := 'New booking created successfully';
        is_booked := true;
        booking_message := 'New booking created successfully within slot';
    END IF;

    -- Prepare booking details JSON
    resp_data := json_build_object(
        'booking_id', booking_ids,
        'capacity_id', capacity_id_,
        'booking_details', booking_details_
    );

    -- Prepare user context for updating service request
    user_context_json := json_build_object(
        'org_id', org_id_,
        'usr_id', usr_id_,
        'ip_address', ip_address_,
        'user_agent', user_agent_,
        'srvc_type_id', srvc_type_id_
    );

    booking_update_json := jsonb_set(user_context_json::jsonb, '{booking_id}', to_jsonb(booking_ids), true);
    booking_update_json := jsonb_set(booking_update_json::jsonb, '{capacity_id}', to_jsonb(capacity_id_), true);
    booking_update_json := jsonb_set(booking_update_json::jsonb, '{booking_details}', to_jsonb(resp_data->'booking_details'), true);
    booking_update_json := jsonb_set(booking_update_json::jsonb, '{is_booked}', to_jsonb(is_booked), true);
    booking_update_json := jsonb_set(booking_update_json::jsonb, '{booking_message}', to_jsonb(booking_message), true);

    PERFORM tms_create_service_request(booking_update_json, ins_id_::integer);

    status := true;

    RETURN json_build_object('status', status, 'message', message, 'data', resp_data);
END;
$function$;

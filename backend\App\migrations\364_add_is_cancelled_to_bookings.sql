BEGIN;

-- Add is_cancelled column to cl_tx_bookings table
ALTER TABLE public.cl_tx_bookings 
ADD COLUMN IF NOT EXISTS is_cancelled boolean DEFAULT false;

-- Create index for better query performance on cancelled bookings
CREATE INDEX IF NOT EXISTS cl_tx_bookings_is_cancelled_idx 
ON public.cl_tx_bookings (is_cancelled);

-- Create composite index for order_id and is_cancelled for better performance
CREATE INDEX IF NOT EXISTS cl_tx_bookings_order_id_is_cancelled_idx 
ON public.cl_tx_bookings (order_id, is_cancelled);

END;
